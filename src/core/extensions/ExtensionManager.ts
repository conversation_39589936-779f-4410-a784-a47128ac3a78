import { promises as fs } from 'fs';
import * as path from 'path';

import { BrowserWindow, app } from 'electron';
import { v4 as uuidv4 } from 'uuid';

interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  permissions: string[];
  enabled: boolean;
  path: string;
  manifest: any;
  icon?: string;
  homepage?: string;
  updateUrl?: string;
  lastUpdateCheck?: number;
  size: number;
  installDate: number;
  updateDate: number;
}

interface ExtensionSettings {
  autoUpdate: boolean;
  updateCheckInterval: number;
  allowInPrivate: boolean;
  allowedPermissions: string[];
  blockedPermissions: string[];
  maxExtensions: number;
}

export class ExtensionManager {
  private static instance: ExtensionManager;
  private extensions: Map<string, Extension>;
  private settings: ExtensionSettings;
  private updateInterval: NodeJS.Timeout | null = null;
  private mainWindow: BrowserWindow | null = null;

  private constructor() {
    this.extensions = new Map();
    this.settings = {
      autoUpdate: true,
      updateCheckInterval: 24 * 60 * 60 * 1000, // 24 hours
      allowInPrivate: false,
      allowedPermissions: ['storage', 'tabs', 'bookmarks', 'history', 'notifications'],
      blockedPermissions: ['debugger', 'proxy', 'system.cpu', 'system.memory'],
      maxExtensions: 50,
    };
  }

  public static getInstance(): ExtensionManager {
    if (!ExtensionManager.instance) {
      ExtensionManager.instance = new ExtensionManager();
    }
    return ExtensionManager.instance;
  }

  public async initialize(window: BrowserWindow): Promise<void> {
    this.mainWindow = window;
    await this.loadExtensions();
    await this.loadSettings();
    if (this.settings.autoUpdate) {
      this.startUpdateInterval();
    }
  }

  private async loadExtensions(): Promise<void> {
    try {
      const extensionsDir = path.join(app.getPath('userData'), 'extensions');
      await fs.mkdir(extensionsDir, { recursive: true });

      const entries = await fs.readdir(extensionsDir, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          try {
            const manifestPath = path.join(extensionsDir, entry.name, 'manifest.json');
            const manifestContent = await fs.readFile(manifestPath, 'utf-8');
            const manifest = JSON.parse(manifestContent);

            const stats = await fs.stat(path.join(extensionsDir, entry.name));

            const extension: Extension = {
              id: entry.name,
              name: manifest.name,
              version: manifest.version,
              description: manifest.description,
              author: manifest.author,
              permissions: manifest.permissions || [],
              enabled: true,
              path: path.join(extensionsDir, entry.name),
              manifest,
              icon: manifest.icons?.['128'] || manifest.icons?.['48'],
              homepage: manifest.homepage_url,
              updateUrl: manifest.update_url,
              size: stats.size,
              installDate: stats.birthtimeMs,
              updateDate: stats.mtimeMs,
            };

            this.extensions.set(extension.id, extension);
          } catch (error) {
            console.error(`Failed to load extension ${entry.name}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load extensions:', error);
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'extension-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      // If settings don't exist, use defaults
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'extension-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private startUpdateInterval(): void {
    this.updateInterval = setInterval(() => {
      this.checkForUpdates();
    }, this.settings.updateCheckInterval);
  }

  private async checkForUpdates(): Promise<void> {
    for (const extension of this.extensions.values()) {
      if (extension.updateUrl) {
        try {
          // Implement update check logic here
          // This would typically involve:
          // 1. Fetching the update manifest
          // 2. Comparing versions
          // 3. Downloading and installing updates if available
        } catch (error) {
          console.error(`Failed to check for updates for ${extension.name}:`, error);
        }
      }
    }
  }

  public async installExtension(extensionPath: string): Promise<Extension> {
    if (this.extensions.size >= this.settings.maxExtensions) {
      throw new Error('Maximum number of extensions reached');
    }

    try {
      const manifestPath = path.join(extensionPath, 'manifest.json');
      const manifestContent = await fs.readFile(manifestPath, 'utf-8');
      const manifest = JSON.parse(manifestContent);

      // Validate permissions
      const invalidPermissions = manifest.permissions?.filter(
        (p: string) =>
          !this.settings.allowedPermissions.includes(p) ||
          this.settings.blockedPermissions.includes(p)
      );

      if (invalidPermissions?.length > 0) {
        throw new Error(`Extension requires blocked permissions: ${invalidPermissions.join(', ')}`);
      }

      const extensionId = uuidv4();
      const targetPath = path.join(app.getPath('userData'), 'extensions', extensionId);

      await fs.mkdir(targetPath, { recursive: true });
      await this.copyDirectory(extensionPath, targetPath);

      const stats = await fs.stat(targetPath);

      const extension: Extension = {
        id: extensionId,
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: manifest.author,
        permissions: manifest.permissions || [],
        enabled: true,
        path: targetPath,
        manifest,
        icon: manifest.icons?.['128'] || manifest.icons?.['48'],
        homepage: manifest.homepage_url,
        updateUrl: manifest.update_url,
        size: stats.size,
        installDate: Date.now(),
        updateDate: Date.now(),
      };

      this.extensions.set(extension.id, extension);
      return extension;
    } catch (error) {
      throw new Error(`Failed to install extension: ${error.message}`);
    }
  }

  private async copyDirectory(src: string, dest: string): Promise<void> {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  public async uninstallExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (!extension) return;

    try {
      await fs.rm(extension.path, { recursive: true, force: true });
      this.extensions.delete(id);
    } catch (error) {
      throw new Error(`Failed to uninstall extension: ${error.message}`);
    }
  }

  public async enableExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (extension) {
      extension.enabled = true;
      extension.updateDate = Date.now();
    }
  }

  public async disableExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (extension) {
      extension.enabled = false;
      extension.updateDate = Date.now();
    }
  }

  public getExtension(id: string): Extension | undefined {
    return this.extensions.get(id);
  }

  public getAllExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  public getEnabledExtensions(): Extension[] {
    return Array.from(this.extensions.values()).filter(extension => extension.enabled);
  }

  public getSettings(): ExtensionSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<ExtensionSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();

    if (this.settings.autoUpdate) {
      this.startUpdateInterval();
    } else if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  }

  public cleanup(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  }
}
