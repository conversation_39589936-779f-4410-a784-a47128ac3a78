import { BookmarkManager } from './BookmarkManager';
import { SettingsManager } from './SettingsManager';
import { SessionManager } from './SessionManager';
import { UpdateManager } from './UpdateManager';

// 1. Create a registry that maps a service name to its type.
interface ServiceRegistry {
  settingsManager: SettingsManager;
  sessionManager: SessionManager;
  updateManager: UpdateManager;
  bookmarkManager: BookmarkManager;
}

type ServiceName = keyof ServiceRegistry;

class ServiceContainer {
  private services = new Map<ServiceName, ServiceRegistry[ServiceName]>();

  public register<K extends ServiceName>(name: K, service: ServiceRegistry[K]): void {
    if (this.services.has(name)) {
      console.warn(`Service with name "${name}" is already registered.`);
      return;
    }
    this.services.set(name, service);
  }

  public get<K extends ServiceName>(name: K): ServiceRegistry[K] {
    const service = this.services.get(name);
    if (!service) {
      throw new Error(`Service with name "${name}" is not registered.`);
    }
    return service;
  }
}

export const container = new ServiceContainer();