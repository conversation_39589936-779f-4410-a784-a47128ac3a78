# 🤝 A14 Browser Contributing Guide

We are incredibly excited that you are interested in contributing to A14 Browser! Any help is welcome. This guide will help you get started.

## 📜 Code of Conduct

First and foremost, please read our [Code of Conduct](./docs/CODE_OF_CONDUCT.md). We expect all participants to follow it to maintain a friendly and respectful atmosphere.

## 🚀 How Can I Help?

There are many ways to contribute:

*   **🐛 Reporting Bugs:** Found a bug? [Create an Issue](https://github.com/a14browser/a14browser/issues/new?assignees=&labels=bug&template=bug_report.md&title=) with a detailed description.
*   **💡 Suggesting New Features:** Have an idea to make the browser better? [Create an Issue](https://github.com/a14browser/a14browser/issues/new?assignees=&labels=enhancement&template=feature_request.md&title=) and describe it.
*   **📝 Improving Documentation:** Found a typo or think a section could be explained better? Feel free to send a Pull Request!
*   **💻 Writing Code:** Want to fix a bug or implement a new feature? That's great!

## 🛠️ Development Environment Setup

1.  **Fork the repository** on GitHub.
2.  **Clone your fork** to your local machine:
    ```bash
    git clone https://github.com/YOUR_USERNAME/a14browser.git
    cd a14browser
    ```
3.  **Add the main repository** as an `upstream` remote:
    ```bash
    git remote add upstream https://github.com/a14browser/a14browser.git
    ```
4.  **Install dependencies.** We use `npm ci` to ensure a consistent environment.
    ```bash
    npm ci
    ```
5.  **Run the project** in development mode:
    ```bash
    npm run dev
    ```

## 🌿 Development Process (Git Flow)

1.  **Sync your `main` branch** with `upstream`:
    ```bash
    git checkout main
    git pull upstream main
    ```
2.  **Create a new branch** for your task. Name branches descriptively:
    *   For new features: `feat/feature-name` (e.g., `feat/reading-mode`)
    *   For fixes: `fix/what-is-fixed` (e.g., `fix/settings-crash`)
    ```bash
    git checkout -b feat/my-new-feature
    ```
3.  **Write code!** Remember to follow the project's code style.

4.  **Add tests** for new code. Make sure all tests pass:
    ```bash
    npm test
    ```
5.  **Commit your changes.** We follow the Conventional Commits specification. This helps automate changelog generation.
    *   `feat: add reading mode button`
    *   `fix: resolve crash when opening bookmarks`
    *   `docs: update contributing guide`
    ```bash
    git add .
    git commit -m "feat: your new feature"
    ```
6.  **Push your branch** to your fork:
    ```bash
    git push -u origin feat/my-new-feature
    ```
7.  **Create a Pull Request (PR)** from your fork to the `main` branch of the main repository.
    *   Give the PR a meaningful title and a detailed description.
    *   If your PR fixes an existing Issue, mention `Closes #123` in the description.

## 🎨 Code Style

We use **ESLint** for static analysis and **Prettier** for formatting. Before committing, please run:

```bash
# Check and automatically fix linter errors
npm run lint -- --fix

# Format all code
npm run format
```

Спасибо за ваш вклад! ❤️