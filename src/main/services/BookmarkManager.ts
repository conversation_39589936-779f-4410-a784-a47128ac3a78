import Store from 'electron-store';
import { v4 as uuidv4 } from 'uuid';
import { Bookmark } from '../../shared/types/bookmarks';

interface BookmarkState {
  bookmarks: Bookmark[];
}

export class BookmarkManager {
  private store: Store<BookmarkState>;

  constructor() {
    this.store = new Store<BookmarkState>({
      name: 'bookmarks',
      defaults: { bookmarks: [] },
    });
  }

  public getAll(): Bookmark[] {
    return this.store.get('bookmarks').sort((a, b) => b.createdAt - a.createdAt);
  }

  public add(title: string, url: string): Bookmark {
    const newBookmark: Bookmark = { id: uuidv4(), title, url, createdAt: Date.now() };
    const currentBookmarks = this.store.get('bookmarks');
    this.store.set('bookmarks', [...currentBookmarks, newBookmark]);
    return newBookmark;
  }

  public remove(id: string): void {
    const currentBookmarks = this.store.get('bookmarks');
    const updatedBookmarks = currentBookmarks.filter(b => b.id !== id);
    this.store.set('bookmarks', updatedBookmarks);
  }
}