const path = require('path');

const { app } = require('electron');

const { ExtensionLoader } = require('../extensions/extension-loader');

const { AdBlockerManager } = require('./adblocker/adBlockerManager');
const { ConfigManager } = require('./config/configManager');
const DependencyInjector = require('./core/dependencyInjector');

// Import and initialize IPC modules
const { initializeAdBlockerIPC } = require('./ipc/adBlockerIPC');
const { initializeBookmarkIPC } = require('./ipc/bookmarkIPC');
const { initializeExtensionIPC } = require('./ipc/extensionIPC');
const { initializeHistoryIPC } = require('./ipc/historyIPC');
const { IPCManager } = require('./ipc/ipcManager');
const { initializeNavigationIPC } = require('./ipc/navigationIPC');
const { initializeSettingsIPC } = require('./ipc/settingsIPC');
const { initializeSystemIPC } = require('./ipc/systemIPC');
const { initializeTabIPC } = require('./ipc/tabIPC');
const { initializeThemeIPC } = require('./ipc/themeIPC');
const LifecycleManager = require('./lifecycleManager');
const { SessionManager } = require('./session/sessionManager');
const ErrorHandler = require('./utils/errorHandling');
const { initializeLogger } = require('./utils/logger');
const WindowManager = require('./windowManager');

/**
 * @class AppInitializer
 * @description Orchestrates the initialization of the entire application, including dependency injection,
 * window management, lifecycle management, ad blocking, extension loading, session management,
 * configuration management, and Inter-Process Communication (IPC).
 */
class AppInitializer {
  /**
   * Initializes the AppInitializer, setting up dependency injection and resolving core managers.
   */
  constructor() {
    /**
     * @property {DependencyInjector} injector - The dependency injector instance.
     */
    this.injector = new DependencyInjector();
    this._registerDependencies();

    /**
     * @property {WindowManager} windowManager - Manages the creation and lifecycle of application windows.
     */
    this.windowManager = this.injector.resolve('WindowManager');
    /**
     * @property {ConfigManager} configManager - Manages application configuration settings.
     */
    this.configManager = this.injector.resolve('ConfigManager');
    /**
     * @property {SessionManager} sessionManager - Manages user sessions and related data.
     */
    this.sessionManager = this.injector.resolve('SessionManager');
    /**
     * @property {ExtensionLoader} extensionLoader - Handles loading and managing browser extensions.
     */
    this.extensionLoader = this.injector.resolve('ExtensionLoader');
    /**
     * @property {AdBlockerManager} adBlockerManager - Manages ad-blocking functionality.
     */
    this.adBlockerManager = this.injector.resolve('AdBlockerManager');
    /**
     * @property {LifecycleManager} lifecycleManager - Manages application lifecycle events.
     */
    this.lifecycleManager = this.injector.resolve('LifecycleManager');
    /**
     * @property {ErrorHandler} errorHandler - Manages application-wide error handling.
     */
    this.errorHandler = this.injector.resolve('ErrorHandler');
    /**
     * @property {IPCManager|null} ipcManager - Manages Inter-Process Communication (IPC) handlers.
     * Initialized after the main window is created.
     */
    this.ipcManager = null; // IPCManager is initialized after mainWindow creation
    /**
     * @property {Electron.BrowserWindow|null} mainWindow - The main application window.
     */
    this.mainWindow = null;
  }

  /**
   * Clears all application data and state for cleanup on quit.
   * @async
   * @returns {Promise<void>}
   */
  async clearAllData() {
    this.errorHandler.wrapSync(() => {
      global.logger.info('Starting application data cleanup...');
    }, 'AppInitializer:clearAllData:start');

    try {
      // Clear session data
      if (this.sessionManager && typeof this.sessionManager.clearSession === 'function') {
        await this.sessionManager.clearSession();
        global.logger.info('Session data cleared.');
      }

      // Unload all extensions
      if (this.extensionLoader && typeof this.extensionLoader.unloadAllExtensions === 'function') {
        await this.extensionLoader.unloadAllExtensions();
        global.logger.info('Extensions unloaded.');
      }

      // Reset ad blocker
      if (this.adBlockerManager && typeof this.adBlockerManager.reset === 'function') {
        await this.adBlockerManager.reset();
        global.logger.info('AdBlocker reset.');
      }

      // Additional cleanup can be added here

      global.logger.info('Application data cleanup completed.');
    } catch (error) {
      global.logger.error('Error during application data cleanup:', error);
    }
  }

  /**
   * Registers core application dependencies with the dependency injector.
   * This includes Electron's 'app' object, various managers, and the config store.
   * @private
   */
  _registerDependencies() {
    this.injector.register('app', app, true);
    this.injector.register('WindowManager', WindowManager, true);
    this.injector.register('SessionManager', SessionManager);
    this.injector.register('ExtensionLoader', ExtensionLoader, true);
    this.injector.register('AdBlockerManager', AdBlockerManager);
    this.injector.register('LifecycleManager', LifecycleManager);
    this.injector.register('ErrorHandler', ErrorHandler, true);

    // Register instances that require constructor arguments or special setup
    this.injector.register('configStore', this.injector.resolve('ConfigManager').store, true);
  }

  /**
   * Asynchronously initializes the application components.
   * This includes initializing the logger, ad blocker, creating the main window, and setting up lifecycle management.
   * @async
   * @returns {Promise<void>}
   */
  async initialize() {
    // Initialize logger after app is ready and make it global
    initializeLogger(this.injector.resolve('app'));
    this.errorHandler.wrapSync(() => {}, 'AppInitializer:loggerInitialized');
    this.errorHandler.wrapSync(() => {}, 'AppInitializer:initialize');

    // Initialize AdBlockerManager before window creation
    await this.adBlockerManager.initialize();
    this.errorHandler.wrapSync(() => {}, 'AppInitializer:adBlockerInitialized');
    this.createWindow();
    this.errorHandler.wrapSync(() => {}, 'AppInitializer:mainWindowCreated');
    this.lifecycleManager.initialize();
  }

  /**
   * Creates the main Electron browser window.
   * Also initializes the extension loader, session manager, and IPC manager with the new window.
   * @returns {void}
   */
  createWindow() {
    // Using path.join for cross-platform compatibility in path resolution.
    this.errorHandler.wrapSync(() => {
      this.mainWindow = this.windowManager.createWindow(
        path.join(__dirname, '..', 'preload.js'),
        path.join(__dirname, '..', '..', 'renderer', 'index.html')
      );
    }, 'createWindow');

    this.extensionLoader.init(this.mainWindow);
    this.sessionManager.initialize(this.mainWindow);

    // IPCManager needs mainWindow during construction
    this.ipcManager = new IPCManager(this.mainWindow);
    // Register the instance
    this.injector.register('IPCManager', this.ipcManager, true);
    // Initialize default IPC handlers
    this.ipcManager.initHandlers();
    this._registerIPCHandlers();
    this.errorHandler.wrapSync(() => {}, 'AppInitializer:ipcManagerInitialized');
  }

  /**
   * Returns the main Electron browser window instance.
   * @returns {Electron.BrowserWindow} The main window.
   */
  getMainWindow() {
    return this.mainWindow;
  }

  /**
   * Registers specialized IPC handlers for various application functionalities.
   * Each handler is responsible for a specific domain of inter-process communication.
   * @private
   * @returns {void}
   */
  _registerIPCHandlers() {
    initializeSystemIPC(this.ipcManager);
    initializeAdBlockerIPC(this.ipcManager, this.adBlockerManager);
    initializeThemeIPC(this.ipcManager);
    initializeExtensionIPC(this.ipcManager, this.mainWindow);
    initializeBookmarkIPC(this.ipcManager, this.configManager);
    initializeHistoryIPC(this.ipcManager, this.configManager);
    initializeNavigationIPC(this.ipcManager, this.mainWindow);
    initializeTabIPC(this.ipcManager, this.mainWindow);
    initializeSettingsIPC(this.ipcManager, this.configManager);
  }
}

module.exports = AppInitializer;
